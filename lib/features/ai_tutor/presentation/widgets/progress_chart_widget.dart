import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';

/// Enhanced progress chart widget with interactive charts and analytics
/// Displays learning progress, quiz scores, and study streaks
class ProgressChartWidget extends StatefulWidget {
  final List<ProgressData>? progressData;
  final String chartType;
  final String title;

  const ProgressChartWidget({
    super.key,
    this.progressData,
    this.chartType = 'line',
    this.title = 'Learning Progress',
  });

  @override
  State<ProgressChartWidget> createState() => _ProgressChartWidgetState();
}

class _ProgressChartWidgetState extends State<ProgressChartWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedPeriod = 'Week';
  String _selectedMetric = 'Score';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // TODO: Replace mock data with real user progress data from repository
    final mockData = widget.progressData ?? _generateMockProgressData();

    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and controls
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.title,
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                Row(
                  children: [
                    // Period selector
                    DropdownButton<String>(
                      value: _selectedPeriod,
                      items: ['Day', 'Week', 'Month', 'Year']
                          .map(
                            (period) => DropdownMenuItem(
                              value: period,
                              child: Text(period),
                            ),
                          )
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedPeriod = value!;
                        });
                      },
                    ),
                    const SizedBox(width: 8),
                    // Metric selector
                    DropdownButton<String>(
                      value: _selectedMetric,
                      items: ['Score', 'Time', 'Streak']
                          .map(
                            (metric) => DropdownMenuItem(
                              value: metric,
                              child: Text(metric),
                            ),
                          )
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedMetric = value!;
                        });
                      },
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Tab bar for different chart types
            TabBar(
              controller: _tabController,
              labelColor: Theme.of(context).primaryColor,
              unselectedLabelColor: Colors.grey,
              indicatorColor: Theme.of(context).primaryColor,
              tabs: const [
                Tab(icon: Icon(Icons.show_chart), text: 'Line'),
                Tab(icon: Icon(Icons.bar_chart), text: 'Bar'),
                Tab(icon: Icon(Icons.pie_chart), text: 'Pie'),
              ],
            ),
            const SizedBox(height: 16),

            // Chart content
            SizedBox(
              height: 300,
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildLineChart(mockData),
                  _buildBarChart(mockData),
                  _buildPieChart(mockData),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Summary statistics
            _buildSummaryStats(mockData),
          ],
        ),
      ),
    );
  }

  Widget _buildLineChart(List<ProgressData> data) {
    return LineChart(
      LineChartData(
        gridData: FlGridData(show: true),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: const TextStyle(fontSize: 12),
                );
              },
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < data.length) {
                  return Text(
                    '${data[index].date.day}/${data[index].date.month}',
                    style: const TextStyle(fontSize: 10),
                  );
                }
                return const Text('');
              },
            ),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        borderData: FlBorderData(show: true),
        lineBarsData: [
          LineChartBarData(
            spots: data
                .asMap()
                .entries
                .map(
                  (entry) => FlSpot(
                    entry.key.toDouble(),
                    entry.value.score.toDouble(),
                  ),
                )
                .toList(),
            isCurved: true,
            color: Theme.of(context).primaryColor,
            barWidth: 3,
            dotData: const FlDotData(show: true),
            belowBarData: BarAreaData(
              show: true,
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBarChart(List<ProgressData> data) {
    return BarChart(
      BarChartData(
        gridData: FlGridData(show: true),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: const TextStyle(fontSize: 12),
                );
              },
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < data.length) {
                  return Text(
                    '${data[index].date.day}/${data[index].date.month}',
                    style: const TextStyle(fontSize: 10),
                  );
                }
                return const Text('');
              },
            ),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        borderData: FlBorderData(show: true),
        barGroups: data
            .asMap()
            .entries
            .map(
              (entry) => BarChartGroupData(
                x: entry.key,
                barRods: [
                  BarChartRodData(
                    toY: entry.value.score.toDouble(),
                    color: Theme.of(context).primaryColor,
                    width: 16,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(4),
                      topRight: Radius.circular(4),
                    ),
                  ),
                ],
              ),
            )
            .toList(),
      ),
    );
  }

  Widget _buildPieChart(List<ProgressData> data) {
    // TODO: Implement subject-wise progress breakdown when multiple subjects are supported
    final subjectData = {
      'Mathematics': 35.0,
      'Science': 25.0,
      'Language': 20.0,
      'History': 20.0,
    };

    return PieChart(
      PieChartData(
        sections: subjectData.entries
            .map(
              (entry) => PieChartSectionData(
                value: entry.value,
                title: '${entry.value.toInt()}%',
                color: _getColorForSubject(entry.key),
                radius: 100,
                titleStyle: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            )
            .toList(),
        centerSpaceRadius: 40,
        sectionsSpace: 2,
      ),
    );
  }

  Widget _buildSummaryStats(List<ProgressData> data) {
    final avgScore = data.isEmpty
        ? 0.0
        : data.map((d) => d.score).reduce((a, b) => a + b) / data.length;
    final totalSessions = data.length;
    final bestScore = data.isEmpty
        ? 0
        : data.map((d) => d.score).reduce((a, b) => a > b ? a : b);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildStatItem(
          'Avg Score',
          '${avgScore.toStringAsFixed(1)}%',
          Icons.trending_up,
        ),
        _buildStatItem('Sessions', totalSessions.toString(), Icons.school),
        _buildStatItem('Best Score', '$bestScore%', Icons.star),
      ],
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Theme.of(context).primaryColor),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
      ],
    );
  }

  Color _getColorForSubject(String subject) {
    switch (subject) {
      case 'Mathematics':
        return Colors.blue;
      case 'Science':
        return Colors.green;
      case 'Language':
        return Colors.orange;
      case 'History':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  // TODO: Replace with real data from user progress repository
  List<ProgressData> _generateMockProgressData() {
    final now = DateTime.now();
    return List.generate(7, (index) {
      return ProgressData(
        date: now.subtract(Duration(days: 6 - index)),
        score:
            60 + (index * 5) + (index % 3 * 10), // Mock progressive improvement
        timeSpent: Duration(minutes: 30 + (index * 5)),
        subject: 'Mathematics',
      );
    });
  }
}

/// Data model for progress tracking
class ProgressData {
  final DateTime date;
  final int score;
  final Duration timeSpent;
  final String subject;

  ProgressData({
    required this.date,
    required this.score,
    required this.timeSpent,
    required this.subject,
  });
}
